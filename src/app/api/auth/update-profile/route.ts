import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser } from '@/lib/local-auth'
import { query } from '@/lib/local-db'
import { handleEmailChangeCompanyAssociation } from '@/lib/email-change-company-association'

export async function PATCH(request: NextRequest) {
  try {
    const user = await getCurrentUser()
    
    if (!user) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { firstName, lastName, email } = body

    if (!firstName || !lastName || !email) {
      return NextResponse.json(
        { error: 'First name, last name, and email are required' },
        { status: 400 }
      )
    }

    // Check if email is already taken by another user
    if (email !== user.email) {
      const existingUser = await query(
        'SELECT id FROM users WHERE email = $1 AND id != $2',
        [email.toLowerCase(), user.id]
      )

      if (existingUser.rows.length > 0) {
        return NextResponse.json(
          { error: 'Email is already taken' },
          { status: 400 }
        )
      }
    }

    // Handle company association changes if email is being updated
    let companyAssociationResult = null
    if (email.toLowerCase() !== user.email.toLowerCase()) {
      console.log('📧 Email change detected, processing company association:', {
        oldEmail: user.email,
        newEmail: email.toLowerCase(),
        userId: user.id
      })

      companyAssociationResult = await handleEmailChangeCompanyAssociation(
        user.id,
        user.email,
        email.toLowerCase(),
        firstName
      )

      console.log('🏢 Company association result:', companyAssociationResult)
    }

    // Update user profile
    const result = await query(
      `UPDATE users
       SET first_name = $1, last_name = $2, email = $3, updated_at = NOW()
       WHERE id = $4
       RETURNING id, email, first_name, last_name, role, email_verified, created_at, company_id`,
      [firstName, lastName, email.toLowerCase(), user.id]
    )

    const updatedUser = result.rows[0]

    // Prepare response with company association information
    const response: {
      user: {
        id: string;
        email: string;
        firstName: string;
        lastName: string;
        role: string;
        emailVerified: boolean;
        companyId?: string;
        companyName?: string;
      };
      companyAssociation?: {
        status: string;
        message: string;
      };
    } = {
      user: {
        id: updatedUser.id,
        email: updatedUser.email,
        firstName: updatedUser.first_name,
        lastName: updatedUser.last_name,
        role: updatedUser.role,
        emailVerified: updatedUser.email_verified,
        createdAt: updatedUser.created_at,
        companyId: updatedUser.company_id,
      }
    }

    // Include company association information in response
    if (companyAssociationResult) {
      response.companyAssociation = {
        action: companyAssociationResult.action,
        message: companyAssociationResult.message,
        ...(companyAssociationResult.companyName && { companyName: companyAssociationResult.companyName }),
        ...(companyAssociationResult.action === 'verification_required' && {
          verificationRequired: true,
          verificationMessage: 'Please check your email for a company verification link.'
        })
      }
    }

    return NextResponse.json(response)

  } catch (error) {
    console.error('Error updating profile:', error)
    return NextResponse.json(
      { error: 'Failed to update profile' },
      { status: 500 }
    )
  }
}
