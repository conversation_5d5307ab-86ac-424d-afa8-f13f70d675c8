import { Pool } from 'pg'
import { logger } from './logger'

// Enhanced PostgreSQL connection pool configuration
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  max: process.env.NODE_ENV === 'production' ? 30 : 20, // More connections in production
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: process.env.NODE_ENV === 'production' ? 10000 : 5000, // Longer timeout in production
  allowExitOnIdle: false, // Don't exit when all connections are idle
})

// Pool event handlers for monitoring
pool.on('connect', () => {
  logger.debug('Database connection established', {
    totalCount: pool.totalCount,
    idleCount: pool.idleCount,
    waitingCount: pool.waitingCount
  })
})

pool.on('acquire', () => {
  logger.debug('Database connection acquired from pool', {
    totalCount: pool.totalCount,
    idleCount: pool.idleCount,
    waitingCount: pool.waitingCount
  })
})

pool.on('error', (err: Error) => {
  logger.error('Database pool error', { error: err })
})

pool.on('remove', () => {
  logger.debug('Database connection removed from pool', {
    totalCount: pool.totalCount,
    idleCount: pool.idleCount,
    waitingCount: pool.waitingCount
  })
})

export { pool }

// Helper function to execute queries with monitoring
export async function query<T = any>(text: string, params?: unknown[]): Promise<{ rows: T[]; rowCount: number } & Record<string, unknown>> {
  const startTime = Date.now()
  const client = await pool.connect()

  try {
    const result = await client.query(text, params)
    const duration = Date.now() - startTime

    // Log slow queries (> 1 second)
    if (duration > 1000) {
      logger.warn('Slow database query detected', {
        query: text.substring(0, 100) + (text.length > 100 ? '...' : ''),
        duration,
        rowCount: result.rowCount
      })
    } else {
      logger.databaseQuery(text, duration, { rowCount: result.rowCount })
    }

    return result
  } catch (error) {
    const duration = Date.now() - startTime
    logger.databaseError(text, error as Error, { duration, params })
    throw error
  } finally {
    client.release()
  }
}

// Helper function for transactions with monitoring
export async function transaction<T>(callback: (client: { query: (text: string, params?: unknown[]) => Promise<unknown> }) => Promise<T>): Promise<T> {
  const startTime = Date.now()
  const client = await pool.connect()

  try {
    await client.query('BEGIN')
    logger.debug('Transaction started')

    const result = await callback(client)

    await client.query('COMMIT')
    const duration = Date.now() - startTime
    logger.debug('Transaction committed', { duration })

    return result
  } catch (error) {
    await client.query('ROLLBACK')
    const duration = Date.now() - startTime
    logger.error('Transaction rolled back', { error: error as Error, duration })
    throw error
  } finally {
    client.release()
  }
}

// Database pool monitoring
export function getPoolStats() {
  return {
    totalCount: pool.totalCount,
    idleCount: pool.idleCount,
    waitingCount: pool.waitingCount,
    maxConnections: pool.options.max,
    minConnections: pool.options.min
  }
}

export async function checkDatabaseHealth(): Promise<boolean> {
  try {
    const result = await query('SELECT 1 as health_check')
    return result.rows.length > 0
  } catch (error) {
    logger.error('Database health check failed', { error: error as Error })
    return false
  }
}

// Graceful shutdown with proper cleanup
async function gracefulShutdown(signal: string) {
  logger.info(`Received ${signal}, shutting down gracefully`)

  try {
    await pool.end()
    logger.info('Database pool closed successfully')
  } catch (error) {
    logger.error('Error closing database pool', { error: error as Error })
  }

  process.exit(0)
}

// Only register signal handlers once to prevent memory leaks
if (!process.env.DB_SIGNAL_HANDLERS_REGISTERED) {
  process.env.DB_SIGNAL_HANDLERS_REGISTERED = 'true'
  process.on('SIGINT', () => gracefulShutdown('SIGINT'))
  process.on('SIGTERM', () => gracefulShutdown('SIGTERM'))
}
