import { NextRequest, NextResponse } from 'next/server'
import { getCompanyById } from '@/lib/database'

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const company = await getCompanyById(id)
    
    if (!company) {
      return NextResponse.json(
        { error: 'Company not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(company)
  } catch (error) {
    console.error('Error fetching company:', error)
    return NextResponse.json(
      { error: 'Failed to fetch company' },
      { status: 500 }
    )
  }
}

export async function PATCH(
  _request: NextRequest,
  { params: _params }: { params: Promise<{ id: string }> }
) {
  // Company editing by users is no longer allowed
  // Only admins can edit companies through the admin dashboard
  return NextResponse.json(
    { error: 'Company editing is restricted to administrators' },
    { status: 403 }
  )
}
